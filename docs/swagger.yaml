definitions:
  errcode.Error:
    type: object
  model.Tag:
    properties:
      created_by:
        type: string
      created_on:
        type: integer
      deleted_on:
        type: integer
      id:
        type: integer
      is_del:
        type: integer
      modified_by:
        type: string
      modified_on:
        type: integer
      name:
        type: string
      state:
        type: integer
    type: object
info:
  contact: {}
  description: Go 语言实战项目
  termsOfService: http://swagger.io/terms/
  title: 博客系统
  version: "1.0"
paths:
  /api/v1/tags:
    get:
      description: 获取多个标签
      parameters:
      - description: 标签名称
        in: query
        name: name
        type: string
      - description: 状态
        enum:
        - 0
        - 1
        in: query
        name: state
        type: integer
      - description: 页码
        in: query
        name: page
        type: integer
      - description: 每页数量
        in: query
        name: page_size
        type: integer
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/model.Tag'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/errcode.Error'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/errcode.Error'
      summary: 获取多个标签
    post:
      parameters:
      - description: 标签名称
        in: body
        maxLength: 100
        minLength: 3
        name: name
        required: true
        schema:
          type: string
      - description: 状态
        enum:
        - 0
        - 1
        in: query
        name: state
        type: integer
      - description: 创建者
        in: body
        maxLength: 100
        minLength: 3
        name: created_by
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/model.Tag'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/errcode.Error'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/errcode.Error'
      summary: 创建标签
  /api/v1/tags/{id}:
    delete:
      parameters:
      - description: 标签ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/model.Tag'
        "400":
          description: 请求错误
          schema:
            $ref: '#/definitions/errcode.Error'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/errcode.Error'
      summary: 删除标签
swagger: "2.0"

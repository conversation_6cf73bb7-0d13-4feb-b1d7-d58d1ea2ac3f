package logger

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"runtime"
	"time"
)

type Level int8

type Fields map[string]interface{}

type Logger struct {
    newLogger *log.Logger
	ctx context.Context
	fields Fields
	callers []string
}

func NewLogger(w io.Writer, prefix string, flag int) *Logger {
    l := log.New(w, prefix, flag)
    return &Logger{newLogger: l}
}

func (l *Logger) clone() *Logger {
    nl := *l
    return &nl
}

func (l *Logger) WithFields(f Fields) *Logger {
    ll := l.clone()
    if ll.fields == nil {
        ll.fields = make(Fields)
    }
    for k, v := range f {
        ll.fields[k] = v
    }
    return ll
}

func (l *Logger) WithContext(ctx context.Context) *Logger {
    ll := l.clone()
    ll.ctx = ctx
    return ll
}

func (l *Logger) WithCaller(skip int) *Logger {
    ll := l.clone()
    pc, file, line ,ok := runtime.Caller(skip)
    if ok {
        f := runtime.FuncForPC(pc)
        ll.callers = []string{fmt.Sprintf("%s: %d %s", file, line, f.Name())}
    }
    return ll
}

func (l *Logger) WithCallersFrames() *Logger { 
    maxCallerDepth := 25
    minCallerDepth := 1
    callers := []string{}
    pcs := make([]uintptr, maxCallerDepth)
    depth := runtime.Callers(minCallerDepth, pcs)
    frames := runtime.CallersFrames(pcs[:depth])
    for frame, more := frames.Next(); more; frame, more = frames.Next() {
        callers = append(callers, fmt.Sprintf("%s:%d %s", frame.File, frame.Line, frame.Function))
        if !more {
            break
        }
    }
    ll := l.clone()
    ll.callers = callers
    return ll
}

const (
    LevelDebug Level = iota
    LevelInfo
    LevelWarning
    LevelError
    LevelFatal
    LevelPanic
)

func (l Level) String() string {
    switch l {
    case LevelDebug:
        return "debug"
    case LevelInfo:
        return "info"
    case LevelWarning:
        return "warn"
    case LevelError:
        return "error"
    case LevelFatal:
        return "fatal"
    case LevelPanic:
        return "panic"
    }
    return ""
}

func (l *Logger) JSONFormat(level Level, message string) map[string]interface{} {
    data := make(map[string]interface{})
    data["level"] = level.String()
    data["time"] = time.Now().Local().UnixNano()
    data["message"] = message
    data["caller"] = l.callers
    if len(l.fields) >0 {
        for k,v := range l.fields {
            data[k] = v
        }
    }    
    return data
}

func (l *Logger) Output(level Level, message string) {
    body, _ := json.Marshal(l.JSONFormat(level, message))
    content := string(body)
    switch level {
    case LevelDebug:
        l.newLogger.Println(content)
    case LevelInfo:
        l.newLogger.Println(content)
    case LevelWarning:
        l.newLogger.Println(content)
    case LevelError:
        l.newLogger.Println(content)
    case LevelFatal:
        l.newLogger.Println(content)
    case LevelPanic:
        l.newLogger.Panicln(content)
    }
}

func (l *Logger) Info(v ...interface{}) {
    l.Output(LevelInfo, fmt.Sprint(v...))
}

func (l *Logger) Infof(format string, v ...interface{}) {
    l.Output(LevelInfo, fmt.Sprintf(format, v...))
}

func (l *Logger) Fatal(v ...interface{}) {
    l.Output(LevelFatal, fmt.Sprint(v...))
}

func (l *Logger) Fatalf(format string, v ...interface{}) {
    l.Output(LevelFatal, fmt.Sprintf(format, v...))
}

func (l *Logger) Debug(v ...interface{}) {
    l.Output(LevelDebug, fmt.Sprint(v...))
}

func (l *Logger) Debugf(format string, v ...interface{}) {
    l.Output(LevelDebug, fmt.Sprintf(format, v...))
}

func (l *Logger) Warning(v ...interface{}) {
    l.Output(LevelWarning, fmt.Sprint(v...))
}

func (l *Logger) Warningf(format string, v ...interface{}) {
    l.Output(LevelWarning, fmt.Sprintf(format, v...))
}

func (l *Logger) Error(v ...interface{}) {
    l.Output(LevelError, fmt.Sprint(v...))
}

func (l *Logger) Errorf(format string, v ...interface{}) {
    l.Output(LevelError, fmt.Sprintf(format, v...))
}
